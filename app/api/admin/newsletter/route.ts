import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';
import { sendEmail, createNewsletterEmailHtml } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { 
      subject, 
      content, 
      featuredArtworks, 
      upcomingEvents, 
      authorName 
    } = await request.json();

    // Validate required fields
    if (!subject || !content) {
      return NextResponse.json({ error: 'Subject and content are required' }, { status: 400 });
    }

    // Get all confirmed subscribers (filter out unsubscribed ones in code)
    const subscribersRef = adminDb.collection('subscribers');
    const confirmedSubscribers = await subscribersRef
      .where('isConfirmed', '==', true)
      .get();

    if (confirmedSubscribers.empty) {
      return NextResponse.json({ error: 'No confirmed subscribers found' }, { status: 404 });
    }

    const subscribers = confirmedSubscribers.docs
      .map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name || data.email.split('@')[0], // Use stored name or fallback to email prefix
          email: data.email,
          confirmationToken: data.confirmationToken,
          isConfirmed: data.isConfirmed,
          subscribedAt: data.subscribedAt,
          confirmedAt: data.confirmedAt,
          unsubscribedAt: data.unsubscribedAt
        };
      })
      .filter(subscriber => !subscriber.unsubscribedAt); // Filter out unsubscribed users

    console.log(`Sending newsletter to ${subscribers.length} subscribers`);

    // Generate email HTML once (with image processing) to avoid repeating expensive operations
    console.log('Generating newsletter HTML with image processing...');
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    let emailHtmlTemplate: string;
    let emailAttachments: Array<{ filename: string; content: Buffer; cid: string; contentDisposition: 'inline' }>;
    try {
      const newsletterData = await createNewsletterEmailHtml({
        name: 'SUBSCRIBER_NAME_PLACEHOLDER', // Will be replaced per subscriber
        subject,
        content,
        featuredArtworks: featuredArtworks || [],
        upcomingEvents: upcomingEvents || [],
        authorName: authorName || 'Bela Gallery Team',
        unsubscribeUrl: 'UNSUBSCRIBE_URL_PLACEHOLDER' // Will be replaced per subscriber
      });
      emailHtmlTemplate = newsletterData.html;
      emailAttachments = newsletterData.attachments;
      console.log(`Newsletter HTML generated successfully with ${emailAttachments.length} image attachments`);
    } catch (error) {
      console.error('Failed to generate newsletter HTML:', error);
      return NextResponse.json({ error: 'Failed to generate newsletter content' }, { status: 500 });
    }

    // Send emails to all subscribers
    const emailPromises = subscribers.map(async (subscriber) => {
      try {
        const unsubscribeUrl = `${baseUrl}/api/unsubscribe/${subscriber.confirmationToken}`;
        
        // Replace placeholders with actual subscriber data
        const personalizedEmailHtml = emailHtmlTemplate
          .replace(/SUBSCRIBER_NAME_PLACEHOLDER/g, subscriber.name)
          .replace(/UNSUBSCRIBE_URL_PLACEHOLDER/g, unsubscribeUrl);

        await sendEmail({
          to: subscriber.email,
          subject: `${subject} - Bela Gallery`,
          html: personalizedEmailHtml,
          attachments: emailAttachments,
        });

        console.log(`Newsletter sent to ${subscriber.email}`);
        return { email: subscriber.email, status: 'sent' };
      } catch (error) {
        console.error(`Failed to send newsletter to ${subscriber.email}:`, error);
        return { email: subscriber.email, status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' };
      }
    });

    const results = await Promise.all(emailPromises);
    const successful = results.filter(r => r.status === 'sent').length;
    const failed = results.filter(r => r.status === 'failed').length;

    // Store newsletter in database for record keeping
    await adminDb.collection('newsletters').add({
      subject,
      content,
      featuredArtworks: featuredArtworks || [],
      upcomingEvents: upcomingEvents || [],
      authorName: authorName || 'Bela Gallery Team',
      sentAt: new Date(),
      recipientCount: subscribers.length,
      successfulSends: successful,
      failedSends: failed,
      results
    });

    return NextResponse.json({ 
      message: `Newsletter sent successfully to ${successful} subscribers`,
      successful,
      failed,
      total: subscribers.length,
      results
    }, { status: 200 });

  } catch (error) {
    console.error('Newsletter sending error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// Get newsletter history
export async function GET() {
  try {
    const newslettersRef = adminDb.collection('newsletters');
    const newsletters = await newslettersRef.orderBy('sentAt', 'desc').limit(20).get();

    const newsletterHistory = newsletters.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      sentAt: doc.data().sentAt?.toDate?.()?.toISOString() || doc.data().sentAt
    }));

    return NextResponse.json({ newsletters: newsletterHistory }, { status: 200 });

  } catch (error) {
    console.error('Error fetching newsletter history:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}