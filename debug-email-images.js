// Debug script to test image URL generation
require('dotenv').config({ path: '.env.local' });

// Mock the Firebase Admin SDK for testing
const mockAdminStorage = {
    bucket: () => ({
        file: (path) => ({
            getSignedUrl: async (options) => {
                console.log(`🔧 Mock: Would generate signed URL for ${path}`);
                return [`https://firebasestorage.googleapis.com/v0/b/test-bucket/o/${encodeURIComponent(path)}?alt=media&token=mock-token`];
            }
        })
    })
};

// Test the URL generation logic
async function testImageUrlGeneration() {
    console.log('🧪 Testing image URL generation...');
    console.log('Environment variables:');
    console.log('- NEXT_PUBLIC_STATIC_BUILD:', process.env.NEXT_PUBLIC_STATIC_BUILD);
    console.log('- NEXT_PUBLIC_BASE_URL:', process.env.NEXT_PUBLIC_BASE_URL);
    console.log('- NEXT_PUBLIC_FIREBASE_USE_EMULATOR:', process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR);
    console.log('- NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST:', process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST);
    console.log('- NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET:', process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET);
    console.log('- NEXT_PUBLIC_STORAGE_BASE_PATH:', process.env.NEXT_PUBLIC_STORAGE_BASE_PATH);
    
    // Test different image paths
    const testPaths = [
        '/images/2013/08/test-image.jpg',
        'images/2013/08/test-image.jpg',
        '/images/gallery/artwork.png'
    ];
    
    for (const imagePath of testPaths) {
        console.log(`\n📸 Testing path: ${imagePath}`);
        
        // Test static build mode
        console.log('  Static build mode:');
        const staticUrl = getStaticUrl(imagePath);
        console.log(`    Result: ${staticUrl}`);
        
        // Test Firebase mode (emulator)
        console.log('  Firebase emulator mode:');
        const emulatorUrl = getEmulatorUrl(imagePath);
        console.log(`    Result: ${emulatorUrl}`);
        
        // Test Firebase mode (production)
        console.log('  Firebase production mode:');
        const prodUrl = getProductionUrl(imagePath);
        console.log(`    Result: ${prodUrl}`);
    }
}

function getStaticUrl(imageUrl) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const normalizedPath = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
    return `${baseUrl}${normalizedPath}`;
}

function getEmulatorUrl(imageUrl) {
    const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
    const bucketName = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app';
    const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
    const normalizedPath = imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl;
    const fullPath = `${basePath}/${normalizedPath}`;
    const encodedPath = encodeURIComponent(fullPath);
    return `http://${emulatorHost}/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
}

function getProductionUrl(imageUrl) {
    const bucketName = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app';
    const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
    const normalizedPath = imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl;
    const fullPath = `${basePath}/${normalizedPath}`;
    const encodedPath = encodeURIComponent(fullPath);
    return `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
}

testImageUrlGeneration().catch(console.error);
