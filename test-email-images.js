// Quick test script to debug email image embedding
const { createNewsletterEmailHtml } = require('./lib/email.ts');

async function testEmailImages() {
    console.log('🧪 Testing email image embedding...');
    
    // Set up environment variables for testing
    process.env.NEXT_PUBLIC_STATIC_BUILD = 'false';
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET = 'belagallery-9e01b.firebasestorage.app';
    process.env.NEXT_PUBLIC_STORAGE_BASE_PATH = 'bela-gallery';
    process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR = 'true';
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST = 'localhost:9199';
    
    const testData = {
        name: 'Test User',
        subject: 'Test Newsletter',
        content: 'This is a test newsletter.',
        featuredArtworks: [
            {
                title: 'Test Artwork',
                description: 'A test artwork',
                imageUrl: '/images/2013/08/some-image.jpg',
                galleryUrl: '/gallery/test'
            }
        ],
        upcomingEvents: [],
        authorName: 'Test Author',
        unsubscribeUrl: 'http://localhost:3000/unsubscribe'
    };
    
    try {
        const html = await createNewsletterEmailHtml(testData);
        console.log('✅ Newsletter HTML generated successfully');
        
        // Check if images are embedded
        if (html.includes('data:image/')) {
            console.log('✅ Base64 image embedding detected');
        } else if (html.includes('Artwork Preview')) {
            console.log('⚠️  Placeholder image detected - image embedding failed');
        } else {
            console.log('❌ No image content detected');
        }
        
        // Save HTML for inspection
        require('fs').writeFileSync('test-newsletter.html', html);
        console.log('📄 Test newsletter saved to test-newsletter.html');
        
    } catch (error) {
        console.error('❌ Error generating newsletter:', error);
    }
}

testEmailImages();
