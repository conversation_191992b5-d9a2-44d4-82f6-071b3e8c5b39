import nodemailer from 'nodemailer';
import { adminStorage } from '@/lib/firebase-admin';
import { writeFileSync } from 'fs';

// Server-side image URL resolution for email embedding
async function getServerSideImageUrl(imageUrl: string): Promise<string> {
    if (!imageUrl) return '';

    const isStaticBuild = process.env.NEXT_PUBLIC_STATIC_BUILD === 'true';
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    if (isStaticBuild) {
        // Static mode: convert relative paths to absolute URLs for email compatibility
        const normalizedPath = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
        const absoluteUrl = `${baseUrl}${normalizedPath}`;
        console.log(`📸 Static mode - converting ${imageUrl} to ${absoluteUrl}`);
        return absoluteUrl;
    } else {
        // Firebase Storage mode - try multiple approaches
        const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
        const useEmulator = process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true';
        const bucketName = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app';

        // Remove leading slash for consistency
        const normalizedPath = imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl;
        const fullPath = `${basePath}/${normalizedPath}`;

        console.log(`📸 Firebase Storage - resolving path: ${fullPath} (emulator: ${useEmulator})`);

        if (useEmulator) {
            // For emulator, try emulator URL first, then fallback to production
            const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';
            const encodedPath = encodeURIComponent(fullPath);
            const emulatorUrl = `http://${emulatorHost}/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
            console.log(`📸 Emulator mode - trying ${emulatorUrl}`);

            // Test if emulator URL is accessible
            try {
                const testResponse = await fetch(emulatorUrl, { method: 'HEAD', signal: AbortSignal.timeout(3000) });
                if (testResponse.ok) {
                    console.log(`📸 Emulator URL accessible - using ${emulatorUrl}`);
                    return emulatorUrl;
                }
            } catch (error) {
                console.log(`📸 Emulator not accessible, falling back to production URL`);
            }

            // Fallback to production URL if emulator is not accessible
            const prodUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
            console.log(`📸 Using production fallback: ${prodUrl}`);
            return prodUrl;
        } else {
            // For production, try Admin SDK first, then fallback to direct URL
            try {
                const bucket = adminStorage.bucket();
                const file = bucket.file(fullPath);

                // Get a signed URL that's valid for 1 hour
                const [signedUrl] = await file.getSignedUrl({
                    action: 'read',
                    expires: Date.now() + 60 * 60 * 1000, // 1 hour from now
                });

                console.log(`📸 Firebase Admin SDK - generated signed URL for ${imageUrl}`);
                return signedUrl;
            } catch (error) {
                console.error(`❌ Error with Firebase Admin SDK for ${imageUrl}:`, error);

                // Fallback to direct URL
                const encodedPath = encodeURIComponent(fullPath);
                const directUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;
                console.log(`📸 Using direct URL fallback: ${directUrl}`);
                return directUrl;
            }
        }
    }
}

// Helper function to detect image type from file extension or magic bytes
function detectImageType(buffer: Buffer, filename: string): string {
    // Check magic bytes first (more reliable)
    const firstBytes = buffer.subarray(0, 4);
    
    // JPEG magic bytes: FF D8 FF
    if (firstBytes[0] === 0xFF && firstBytes[1] === 0xD8 && firstBytes[2] === 0xFF) {
        return 'image/jpeg';
    }
    
    // PNG magic bytes: 89 50 4E 47
    if (firstBytes[0] === 0x89 && firstBytes[1] === 0x50 && firstBytes[2] === 0x4E && firstBytes[3] === 0x47) {
        return 'image/png';
    }
    
    // GIF magic bytes: 47 49 46 38
    if (firstBytes[0] === 0x47 && firstBytes[1] === 0x49 && firstBytes[2] === 0x46 && firstBytes[3] === 0x38) {
        return 'image/gif';
    }
    
    // WebP magic bytes: 52 49 46 46 (RIFF) + WEBP at offset 8
    if (firstBytes[0] === 0x52 && firstBytes[1] === 0x49 && firstBytes[2] === 0x46 && firstBytes[3] === 0x46) {
        const webpBytes = buffer.subarray(8, 12);
        if (webpBytes[0] === 0x57 && webpBytes[1] === 0x45 && webpBytes[2] === 0x42 && webpBytes[3] === 0x50) {
            return 'image/webp';
        }
    }
    
    // Fallback to file extension
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
        case 'jpg':
        case 'jpeg':
            return 'image/jpeg';
        case 'png':
            return 'image/png';
        case 'gif':
            return 'image/gif';
        case 'webp':
            return 'image/webp';
        default:
            return 'image/jpeg'; // Default fallback
    }
}

// Helper function to resize image using Canvas API (server-side)
async function resizeImageBuffer(buffer: Buffer, maxWidth: number = 300, quality: number = 0.7): Promise<Buffer> {
    try {
        // Import sharp for server-side image processing
        const sharp = await import('sharp');
        
        // Resize and compress the image
        const resizedBuffer = await sharp.default(buffer)
            .resize(maxWidth, null, {
                withoutEnlargement: true,
                fit: 'inside'
            })
            .jpeg({
                quality: Math.round(quality * 100),
                progressive: true,
                mozjpeg: true
            })
            .toBuffer();
            
        console.log(`📏 Image resized: ${buffer.length} bytes → ${resizedBuffer.length} bytes (${Math.round((1 - resizedBuffer.length / buffer.length) * 100)}% reduction)`);
        
        return resizedBuffer;
    } catch (error) {
        console.warn('⚠️  Sharp not available, using fallback compression');
        
        // Fallback: Simple JPEG quality reduction without resizing
        // This is a basic approach - in production you might want to install sharp
        return buffer;
    }
}

// Alternative resize function using Canvas (if sharp is not available)
async function resizeImageWithCanvas(buffer: Buffer, maxWidth: number = 300): Promise<Buffer> {
    try {
        // This would require canvas package for server-side
        // For now, return original buffer
        console.warn('⚠️  Canvas resizing not implemented, using original image');
        return buffer;
    } catch (error) {
        console.warn('⚠️  Canvas resizing failed, using original image');
        return buffer;
    }
}

// Ensure these environment variables are set by the user
const GMAIL_EMAIL = process.env.GMAIL_EMAIL;
const GMAIL_APP_PASSWORD = process.env.GMAIL_APP_PASSWORD;

interface MailOptions {
  to: string;
  subject: string;
  html: string;
}

// Create a Nodemailer transporter using Gmail
// Note: User needs to configure their Gmail for "App Passwords" for this to work reliably and securely.
// "Less secure app access" is another option but not recommended.
let transporter: nodemailer.Transporter;

if (GMAIL_EMAIL && GMAIL_APP_PASSWORD) {
    transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
            user: GMAIL_EMAIL,
            pass: GMAIL_APP_PASSWORD,
        },
    });
} else {
    console.warn(
        "Email sending is not configured. Missing GMAIL_EMAIL or GMAIL_APP_PASSWORD environment variables. " +
        "Subscription confirmation emails will not be sent."
    );
    // Create a mock transporter that logs to console if not configured
    transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true
    });
}

export async function sendEmail({ to, subject, html }: MailOptions): Promise<void> {
  if (!GMAIL_EMAIL || !GMAIL_APP_PASSWORD) {
    console.log(`Mock email send:
      To: ${to}
      Subject: ${subject}
      HTML: ${html}
      Reason: Email sending is not configured in environment variables.
    `);
    // Potentially throw an error or return a specific status if email sending is critical
    // For now, we log and proceed, assuming console logging is sufficient for missing config.
    return;
  }

  const mailOptions = {
    from: `"Bela Gallery" <${GMAIL_EMAIL}>`, // Sender address (must be your Gmail address)
    to,
    subject,
    html,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Message sent: %s', info.messageId);
    // Preview URL only available with ethereal.email, not Gmail directly
    // console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
  } catch (error) {
    console.error('Error sending email:', error);
    // Rethrow or handle as needed; perhaps a custom error type
    throw new Error(`Failed to send email: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export function createConfirmationEmailHtml(name: string, confirmationUrl: string): string {
    // Enhanced HTML email template matching the welcome email design
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirm Your Subscription</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .button { display: inline-block; padding: 15px 30px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
            .button:hover { background-color: #0056b3; }
            .highlight { background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .link-fallback { font-size: 0.9em; color: #666; margin-top: 15px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 Confirm Your Subscription</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>
                <p>Thank you for your interest in joining the Bela Gallery newsletter! We're excited to share our artistic journey with you.</p>
                
                <div class="highlight">
                    <p><strong>Just one more step:</strong> Please confirm your email address to activate your subscription and start receiving our updates.</p>
                </div>
                
                <p style="text-align: center;">
                    <a href="${confirmationUrl}" class="button">✨ Confirm My Subscription</a>
                </p>
                
                <p>Once confirmed, you'll receive a warm welcome message and start getting updates about:</p>
                <ul>
                    <li>🖼️ Latest artwork and gallery additions</li>
                    <li>🎭 Upcoming events and exhibitions</li>
                    <li>✨ Exclusive behind-the-scenes content</li>
                </ul>
                
                <p>If you did not request this subscription, please ignore this email.</p>
                
                <div class="link-fallback">
                    <p>If the button above doesn't work, please copy and paste this link into your web browser:</p>
                    <p><a href="${confirmationUrl}" style="color: #007bff; word-break: break-all;">${confirmationUrl}</a></p>
                </div>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
}

interface NewsletterData {
  name: string;
  subject: string;
  content: string;
  featuredArtworks: Array<{
    title: string;
    description?: string;
    imageUrl?: string;
    galleryUrl?: string;
  }>;
  upcomingEvents: Array<{
    title: string;
    date: string;
    description?: string;
    location?: string;
  }>;
  authorName: string;
  unsubscribeUrl: string;
}

// Helper function to convert markdown-style formatting to HTML
function convertMarkdownToHtml(text: string): string {
    const html = text
        // Convert **bold** to <strong>
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // Convert *italic* to <em>
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Handle list items more carefully
    const lines = html.split('\n');
    const processedLines: string[] = [];
    let inList = false;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        if (line.startsWith('• ')) {
            if (!inList) {
                processedLines.push('<ul style="margin: 10px 0; padding-left: 20px;">');
                inList = true;
            }
            processedLines.push(`<li>${line.substring(2)}</li>`);
        } else {
            if (inList) {
                processedLines.push('</ul>');
                inList = false;
            }
            if (line) {
                processedLines.push(line);
            }
        }
    }
    
    // Close any open list
    if (inList) {
        processedLines.push('</ul>');
    }
    
    return processedLines.join('\n');
}

// Helper function to check if emulator is available with better detection
async function isEmulatorAvailable(): Promise<boolean> {
    if (process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR !== 'true') {
        return false;
    }

    try {
        const emulatorHost = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST || 'localhost:9199';

        // Try to make a simple request to the emulator
        const testUrl = `http://${emulatorHost}/v0/b/test/o/test?alt=media`;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout (increased)

        const response = await fetch(testUrl, {
            signal: controller.signal,
            method: 'HEAD'
        });

        clearTimeout(timeoutId);

        // Even a 404 means the emulator is running
        console.log(`🔥 Firebase Storage emulator check: status ${response.status} - emulator is running`);
        return response.status !== undefined;
    } catch (error: any) {
        // Check if it's a connection refused error
        if (error.code === 'ECONNREFUSED' || error.message?.includes('ECONNREFUSED') || error.name === 'AbortError') {
            console.log('🔥 Firebase Storage emulator is not running or not accessible. Start it with: npm run em:start');
            return false;
        }

        // Other errors might still mean emulator is running
        console.log(`🔥 Firebase Storage emulator check failed with: ${error.message}, assuming emulator is not available`);
        return false;
    }
}

// Helper function to fetch image and convert to base64 data URL with resizing
async function getImageAsDataUrl(imageUrl: string): Promise<string> {
    if (!imageUrl) return '';

    try {
        // For emulator mode, we'll try to fetch but won't fail if emulator is not available
        const useEmulator = process.env.NEXT_PUBLIC_FIREBASE_USE_EMULATOR === 'true';
        if (useEmulator) {
            console.log(`🔥 Emulator mode detected - will attempt to fetch from emulator for: ${imageUrl}`);
        }

        // Use the server-side method to get the full image URL
        const fullUrl = await getServerSideImageUrl(imageUrl);
        console.log(`📸 Fetching image for resizing and base64 conversion: ${fullUrl}`);

        // Validate that we have a proper URL
        if (!fullUrl || (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://'))) {
            console.warn(`❌ Invalid URL generated: ${fullUrl} from ${imageUrl}. Skipping image embedding.`);
            return '';
        }

        console.log(`📸 Attempting to fetch image from: ${fullUrl}`);

        // Add timeout to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout (increased for better reliability)

        const response = await fetch(fullUrl, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; Newsletter-Service/1.0)',
                'Accept': 'image/*,*/*;q=0.8'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            console.warn(`❌ Failed to fetch image (${response.status} ${response.statusText}): ${fullUrl}`);

            // Try fallback URL construction if the first attempt failed
            if (fullUrl.includes('localhost:9199')) {
                console.log(`🔄 Trying fallback URL construction for emulator...`);
                const bucketName = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'belagallery-9e01b.firebasestorage.app';
                const basePath = process.env.NEXT_PUBLIC_STORAGE_BASE_PATH || 'bela-gallery';
                const normalizedPath = imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl;
                const fullPath = `${basePath}/${normalizedPath}`;
                const encodedPath = encodeURIComponent(fullPath);
                const fallbackUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodedPath}?alt=media`;

                console.log(`🔄 Trying fallback URL: ${fallbackUrl}`);

                const fallbackResponse = await fetch(fallbackUrl, {
                    signal: controller.signal,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (compatible; Newsletter-Service/1.0)',
                        'Accept': 'image/*,*/*;q=0.8'
                    }
                });

                if (fallbackResponse.ok) {
                    console.log(`✅ Fallback URL worked: ${fallbackUrl}`);
                    // Continue with fallbackResponse instead of response
                    const arrayBuffer = await fallbackResponse.arrayBuffer();
                    const originalBuffer = Buffer.from(arrayBuffer);

                    if (originalBuffer.length === 0) {
                        console.warn(`❌ Empty image data received from fallback URL. Skipping image embedding.`);
                        return '';
                    }

                    // Continue with image processing...
                    console.log(`📊 Original image size from fallback: ${originalBuffer.length} bytes`);
                    const resizedBuffer = await resizeImageBuffer(originalBuffer, 300, 0.6);
                    let contentType = fallbackResponse.headers.get('content-type') || 'image/jpeg';

                    if (!contentType || contentType === 'application/octet-stream' || contentType === 'binary/octet-stream') {
                        contentType = detectImageType(originalBuffer, imageUrl);
                    }
                    contentType = 'image/jpeg'; // Force JPEG for email compatibility

                    const base64 = resizedBuffer.toString('base64');
                    console.log(`✅ Successfully converted fallback image to base64: ${imageUrl}`);
                    return `data:${contentType};base64,${base64}`;
                }
            }

            console.warn(`❌ All attempts failed for ${imageUrl}. Skipping image embedding.`);
            return ''; // Return empty string to skip image embedding
        }

        const arrayBuffer = await response.arrayBuffer();
        const originalBuffer = Buffer.from(arrayBuffer);

        // Validate that we actually got image data
        if (originalBuffer.length === 0) {
            console.warn(`❌ Empty image data received for: ${fullUrl}. Skipping image embedding.`);
            return '';
        }

        console.log(`📊 Original image size: ${originalBuffer.length} bytes`);

        // Resize the image to reduce file size
        const resizedBuffer = await resizeImageBuffer(originalBuffer, 300, 0.6); // 300px width, 60% quality

        // Get content type from response header, but validate it
        let contentType = response.headers.get('content-type') || '';

        // If content type is generic or missing, detect from file content and name
        if (!contentType || contentType === 'application/octet-stream' || contentType === 'binary/octet-stream') {
            contentType = detectImageType(originalBuffer, imageUrl);
            console.log(`🔍 Detected image type: ${contentType} for ${imageUrl}`);
        }

        // Force JPEG for email compatibility (smaller file sizes)
        contentType = 'image/jpeg';

        // Check final size after resizing - use stricter limits for email compatibility
        if (resizedBuffer.length > 50000) { // 50KB limit for better email client compatibility
            console.warn(`📏 Resized image still too large (${resizedBuffer.length} bytes), trying more aggressive compression`);

            // Try more aggressive compression
            const moreCompressedBuffer = await resizeImageBuffer(originalBuffer, 200, 0.3); // 200px width, 30% quality

            if (moreCompressedBuffer.length > 50000) {
                console.warn(`📏 Image still too large after aggressive compression (${moreCompressedBuffer.length} bytes), trying ultra compression`);

                // Ultra compression as last resort
                const ultraCompressedBuffer = await resizeImageBuffer(originalBuffer, 150, 0.2); // 150px width, 20% quality

                if (ultraCompressedBuffer.length > 50000) {
                    console.warn(`📏 Image still too large after ultra compression (${ultraCompressedBuffer.length} bytes), skipping embedding`);
                    return '';
                }

                // Use the ultra compressed version
                const base64 = ultraCompressedBuffer.toString('base64');
                console.log(`✅ Successfully converted image to base64 with ultra compression: ${imageUrl} (${ultraCompressedBuffer.length} bytes)`);
                return `data:${contentType};base64,${base64}`;
            }

            // Use the more compressed version
            const base64 = moreCompressedBuffer.toString('base64');
            console.log(`✅ Successfully converted image to base64 with aggressive compression: ${imageUrl} (${moreCompressedBuffer.length} bytes)`);
            return `data:${contentType};base64,${base64}`;
        }

        // Convert to base64 data URL
        const base64 = resizedBuffer.toString('base64');
        const dataUrl = `data:${contentType};base64,${base64}`;

        console.log(`✅ Successfully converted resized image to base64: ${imageUrl} (${resizedBuffer.length} bytes, ${contentType})`);
        console.log(`🔍 Base64 length: ${base64.length} characters`);
        console.log(`🔍 Data URL length: ${dataUrl.length} characters`);

        // Final check: ensure data URL isn't too large for email clients (most support up to ~100KB base64)
        if (dataUrl.length > 100000) {
            console.warn(`📏 Final data URL too large (${dataUrl.length} characters), skipping embedding`);
            return '';
        }

        return dataUrl;
    } catch (error) {
        if (error instanceof Error) {
            console.error(`❌ Error converting image to data URL: ${imageUrl}`, {
                message: error.message,
                name: error.name,
                stack: error.stack
            });
        } else {
            console.error(`❌ Unknown error converting image to data URL: ${imageUrl}`, error);
        }
        // Return empty string to skip image embedding instead of failing
        return '';
    }
}

// Helper function to convert relative URL to full URL
function getFullUrl(url: string): string {
    if (!url) return '';

    // If it's already a full URL, return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // Convert relative URL to full URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const fullUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
    console.log(`🔗 Converting relative URL: ${url} → ${fullUrl}`);
    return fullUrl;
}

export async function createNewsletterEmailHtml(data: NewsletterData): Promise<string> {
    const { name, subject, content, featuredArtworks, upcomingEvents, authorName, unsubscribeUrl } = data;

    console.log(`📧 Creating newsletter email HTML for: ${name}`);
    console.log(`📧 Featured artworks count: ${featuredArtworks.length}`);
    console.log(`📧 Environment: ${process.env.NEXT_PUBLIC_STATIC_BUILD === 'true' ? 'Static' : 'Firebase'}`);
    console.log(`📧 Base URL: ${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}`);

    // Convert markdown-style content to HTML
    const htmlContent = convertMarkdownToHtml(content);

    // Generate featured artworks section with resized embedded images
    let artworksSection = '';
    if (featuredArtworks.length > 0) {
        console.log(`🎨 Processing ${featuredArtworks.length} artworks for newsletter with image resizing...`);

        const artworkItems = await Promise.all(
            featuredArtworks.map(async (artwork, index) => {
                console.log(`🖼️  Processing artwork ${index + 1}: ${artwork.title} (imageUrl: ${artwork.imageUrl})`);

                let imageHtml = '';
                if (artwork.imageUrl) {
                    const imageDataUrl = await getImageAsDataUrl(artwork.imageUrl);
                    if (imageDataUrl) {
                        // Log the first part of the data URL for debugging
                        const dataUrlPreview = imageDataUrl.substring(0, 100) + '...';
                        console.log(`🔍 Generated data URL preview: ${dataUrlPreview}`);
                        console.log(`🔍 Full data URL length: ${imageDataUrl.length} characters`);

                        imageHtml = `<img src="${imageDataUrl}" alt="${artwork.title}" style="max-width: 100%; width: 300px; height: auto; border-radius: 5px; margin-bottom: 10px; display: block; border: 0;" border="0">`;
                        console.log(`✅ Resized image embedded successfully for: ${artwork.title}`);
                    } else {
                        console.log(`⚠️  Image embedding failed for: ${artwork.title}, showing enhanced placeholder`);
                        // Show an enhanced placeholder instead of broken image
                        imageHtml = `<div style="width: 300px; height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; color: white; font-style: italic; text-align: center; font-family: Arial, sans-serif; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border: 2px solid rgba(255,255,255,0.1);">
                            <div style="padding: 20px;">
                                <div style="font-size: 36px; margin-bottom: 12px; opacity: 0.9; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">🖼️</div>
                                <div style="font-size: 18px; font-weight: bold; margin-bottom: 6px; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">Artwork Preview</div>
                                <div style="font-size: 15px; opacity: 0.9; margin-bottom: 8px; font-weight: 500;">${artwork.title}</div>
                                <div style="font-size: 12px; opacity: 0.8; line-height: 1.4;">Click the link below to<br>view the full artwork</div>
                            </div>
                        </div>`;
                    }
                } else {
                    console.log(`⚠️  No imageUrl provided for artwork: ${artwork.title}`);
                }

                return `
                    <div class="artwork-item">
                        ${imageHtml}
                        <h4 style="margin: 10px 0 5px 0; color: #007bff;">${artwork.title}</h4>
                        ${artwork.description ? `<p style="margin: 5px 0;">${artwork.description}</p>` : ''}
                        ${artwork.galleryUrl ? `<p><a href="${getFullUrl(artwork.galleryUrl)}" style="color: #007bff; text-decoration: none; font-weight: bold;">View Full Artwork in Gallery →</a></p>` : ''}
                    </div>
                `;
            })
        );

        artworksSection = `
            <div class="section">
                <h3>🖼️ Featured Artworks</h3>
                ${artworkItems.join('')}
            </div>
        `;

        console.log(`✅ Completed processing all artworks for newsletter`);
    }

    // Generate events section
    const eventsSection = upcomingEvents.length > 0 ? `
        <div class="section">
            <h3>🎭 Upcoming Events</h3>
            ${upcomingEvents.map(event => `
                <div class="event-item">
                    <h4 style="margin: 15px 0 5px 0; color: #007bff;">${event.title}</h4>
                    <p style="margin: 5px 0; font-weight: bold; color: #666;">📅 ${event.date}</p>
                    ${event.location ? `<p style="margin: 5px 0; color: #666;">📍 ${event.location}</p>` : ''}
                    ${event.description ? `<p style="margin: 10px 0;">${event.description}</p>` : ''}
                </div>
            `).join('')}
        </div>
    ` : '';

    const finalHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
            .artwork-item, .event-item { margin: 20px 0; padding: 15px; background-color: #ffffff; border-radius: 5px; border: 1px solid #e9ecef; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
            .signature { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-style: italic; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 ${subject}</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>

                <div style="margin: 20px 0; line-height: 1.8;">
                    ${htmlContent.split('\n').map(line => {
                        const trimmed = line.trim();
                        if (!trimmed) return '';
                        // Don't wrap list elements or already wrapped HTML in paragraphs
                        if (trimmed.startsWith('<ul>') || trimmed.startsWith('<li>') || trimmed.startsWith('</ul>') || trimmed.includes('<strong>') || trimmed.includes('<em>')) {
                            return trimmed;
                        }
                        return `<p>${trimmed}</p>`;
                    }).filter(Boolean).join('')}
                </div>

                ${artworksSection}

                ${eventsSection}

                <div class="signature">
                    <p>With warm regards,<br>
                    <strong>${authorName}</strong></p>
                </div>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;

    // Debug: Save the HTML to a file for inspection
    try {
        const debugHtml = finalHtml.replace(/SUBSCRIBER_NAME_PLACEHOLDER/g, 'Debug User').replace(/UNSUBSCRIBE_URL_PLACEHOLDER/g, 'http://localhost:3000/unsubscribe');
        writeFileSync('debug-newsletter.html', debugHtml);
        console.log(`🔍 Debug: Newsletter HTML saved to debug-newsletter.html`);
    } catch (error) {
        console.log(`⚠️  Could not save debug HTML file:`, error);
    }

    return finalHtml;
}

export function createWelcomeEmailHtml(name: string, unsubscribeUrl?: string): string {
    // Welcome email template with a warm, professional design
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Our Newsletter</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #007bff; margin: 0; }
            .content { background-color: #ffffff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .highlight { background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
            .unsubscribe { font-size: 0.8em; color: #999; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 Welcome to Bela Gallery!</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>
                <p>Welcome to our community! We're thrilled to have you join our newsletter.</p>
                
                <div class="highlight">
                    <p><strong>What to expect:</strong></p>
                    <ul>
                        <li>🖼️ Latest artwork updates and new gallery additions</li>
                        <li>🎭 Upcoming events and exhibitions</li>
                        <li>✨ Behind-the-scenes insights from the artist</li>
                        <li>🎨 Exclusive previews of new collections</li>
                    </ul>
                </div>
                
                <p>Thank you for your interest in our art and for being part of our creative journey. We look forward to sharing beautiful moments and artistic discoveries with you!</p>
                
                <p>Stay inspired,<br>
                <strong>The Bela Gallery Team</strong></p>
            </div>
            <div class="footer">
                <p>&copy; ${new Date().getFullYear()} Bela Gallery. All rights reserved.</p>
                <div class="unsubscribe">
                    <p>You're receiving this email because you subscribed to our newsletter. If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl || '#'}" style="color: #007bff; text-decoration: none;">unsubscribe at any time</a>.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
}